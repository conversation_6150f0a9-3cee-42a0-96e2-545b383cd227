#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DAZ 3D表面测量 - 功能测试
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
from daz_measurement_demo import DAZMeasurement

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

def test_basic_functionality():
    """测试基本功能"""
    print("DAZ 3D表面测量 - 基本功能测试")
    print("="*50)
    
    # 读取数据
    file_path = "N001.TXT"
    measurer = DAZMeasurement(file_path)
    
    # 平面拟合
    print("\n1. 平面拟合测试...")
    measurer.fit_base_plane()
    print("✓ 平面拟合完成")
    
    # 柱子检测
    print("\n2. 柱子检测测试...")
    measurer.detect_pillars()
    print(f"✓ 检测到 {len(measurer.pillar_centers)} 个柱子")
    
    # 测量前3个柱子
    print("\n3. 柱子测量测试...")
    valid_measurements = 0
    
    for i in range(min(3, len(measurer.pillar_centers))):
        print(f"测试柱子 P{i+1}...")
        
        # 测量高度
        height_result = measurer.measure_pillar_height(i)
        if height_result[0] is not None:
            print(f"  高度: {height_result[0]:.3f} μm")
            
            # 测量尺寸
            dimension_result = measurer.measure_pillar_dimensions(i)
            if dimension_result is not None:
                print(f"  X方向95%宽度: {dimension_result.get('x_width_95', 0):.3f} μm")
                print(f"  Y方向95%宽度: {dimension_result.get('y_width_95', 0):.3f} μm")
                valid_measurements += 1
                print("  ✓ 测量成功")
            else:
                print("  ✗ 尺寸测量失败")
        else:
            print("  ✗ 高度测量失败")
    
    print(f"\n测试结果: {valid_measurements}/3 个柱子测量成功")
    
    # 生成简单的可视化
    print("\n4. 生成可视化...")
    
    # 创建一个简单的对比图
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # 原始高度图
    im1 = axes[0].imshow(measurer.height_matrix.T, origin='lower', 
                        extent=[0, measurer.x_coords[-1], 0, measurer.y_coords[-1]], 
                        cmap='viridis', aspect='equal')
    axes[0].set_title('原始高度图')
    axes[0].set_xlabel('X (μm)')
    axes[0].set_ylabel('Y (μm)')
    plt.colorbar(im1, ax=axes[0], label='高度 (μm)')
    
    # 校正后高度图 + 柱子标记
    im2 = axes[1].imshow(measurer.corrected_height.T, origin='lower',
                        extent=[0, measurer.x_coords[-1], 0, measurer.y_coords[-1]], 
                        cmap='viridis', aspect='equal')
    axes[1].set_title(f'校正后高度图 (检测到{len(measurer.pillar_centers)}个柱子)')
    axes[1].set_xlabel('X (μm)')
    axes[1].set_ylabel('Y (μm)')
    
    # 标记柱子中心
    for i, (cx, cy, real_x, real_y) in enumerate(measurer.pillar_centers):
        axes[1].plot(real_x, real_y, 'r+', markersize=8, markeredgewidth=2)
        if i < 5:  # 只标记前5个
            axes[1].text(real_x, real_y + 10, f'P{i+1}', color='red', 
                        ha='center', va='bottom', fontweight='bold', fontsize=8)
    
    plt.colorbar(im2, ax=axes[1], label='相对高度 (μm)')
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('measurement_result.png', dpi=150, bbox_inches='tight')
    print("✓ 可视化结果已保存为 measurement_result.png")
    
    # 显示图片（如果在交互环境中）
    try:
        plt.show()
    except:
        print("注意: 无法显示图片，但已保存到文件")
    
    print("\n5. 生成测量报告...")
    
    # 生成简单的测量报告
    report = []
    report.append("DAZ 3D表面测量报告")
    report.append("="*50)
    report.append(f"文件: {file_path}")
    report.append(f"数据尺寸: {measurer.x_len} x {measurer.y_len}")
    report.append(f"步长: X={measurer.x_step:.6f}μm, Y={measurer.y_step:.6f}μm")
    report.append(f"高度范围: {np.min(measurer.height_matrix):.6f} ~ {np.max(measurer.height_matrix):.6f}μm")
    report.append("")
    report.append("平面拟合结果:")
    report.append(f"  平面方程: z = {measurer.plane_coeffs[0]:.6f}*x + {measurer.plane_coeffs[1]:.6f}*y + {measurer.plane_coeffs[2]:.6f}")
    report.append("")
    report.append(f"柱子检测结果: 共检测到 {len(measurer.pillar_centers)} 个柱子")
    report.append("")
    
    # 添加前几个柱子的详细信息
    report.append("柱子测量结果:")
    report.append(f"{'ID':<4} {'位置(μm)':<20} {'高度(μm)':<12}")
    report.append("-" * 40)
    
    for i in range(min(10, len(measurer.pillar_centers))):
        height_result = measurer.measure_pillar_height(i)
        if height_result[0] is not None:
            center_x, center_y, real_x, real_y = measurer.pillar_centers[i]
            pos_str = f"({real_x:.1f},{real_y:.1f})"
            report.append(f"P{i+1:<3} {pos_str:<20} {height_result[0]:<12.3f}")
    
    # 保存报告
    with open('measurement_report.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("✓ 测量报告已保存为 measurement_report.txt")
    
    print("\n测试完成！")
    print("生成的文件:")
    print("  - measurement_result.png (可视化结果)")
    print("  - measurement_report.txt (测量报告)")

if __name__ == "__main__":
    test_basic_functionality()
