#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DAZ 3D表面测量演示程序
实现基本的平面拟合、柱子检测和尺寸测量功能
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
from scipy import ndimage
from scipy.optimize import minimize
from sklearn.cluster import DBSCAN
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

class DAZMeasurement:
    """DAZ 3D表面测量类"""
    
    def __init__(self, file_path):
        """初始化并读取数据"""
        self.file_path = file_path
        self.load_data()
        
    def load_data(self):
        """读取和解析DAZ数据"""
        print(f"正在读取文件: {self.file_path}")
        
        with open(self.file_path, 'r') as file:
            data = file.read().strip()
        
        data_list = data.split(' ')
        
        # 解析头部信息
        header = data_list[0:4]
        self.x_len, self.y_len = map(int, header[:2])
        self.x_step, self.y_step = map(float, header[2:])
        
        # 解析高度数据
        height_data = list(map(float, data_list[4:]))
        self.height_matrix = np.fliplr(np.array(height_data).reshape(self.y_len, self.x_len).T)
        
        # 生成坐标网格
        self.x_coords = np.arange(self.x_len) * self.x_step
        self.y_coords = np.arange(self.y_len) * self.y_step
        self.X, self.Y = np.meshgrid(self.x_coords, self.y_coords, indexing='ij')
        
        print(f"数据尺寸: {self.x_len} x {self.y_len}")
        print(f"步长: X={self.x_step:.6f}μm, Y={self.y_step:.6f}μm")
        print(f"高度范围: {np.min(self.height_matrix):.6f} ~ {np.max(self.height_matrix):.6f}μm")
        
    def fit_base_plane(self, outlier_ratio=0.3):
        """拟合基准平面，自动排除异常值"""
        print("正在拟合基准平面...")
        
        # 展平数据
        x_flat = self.X.flatten()
        y_flat = self.Y.flatten()
        z_flat = self.height_matrix.flatten()
        
        # 使用统计方法识别异常值
        z_median = np.median(z_flat)
        z_mad = np.median(np.abs(z_flat - z_median))  # 中位数绝对偏差
        
        # 自适应阈值：基于数据分布确定异常值
        threshold = z_median + 3 * z_mad
        normal_mask = z_flat < threshold
        
        # 如果异常值太多，调整阈值
        if np.sum(normal_mask) < len(z_flat) * (1 - outlier_ratio):
            threshold = np.percentile(z_flat, (1 - outlier_ratio) * 100)
            normal_mask = z_flat < threshold
        
        print(f"使用 {np.sum(normal_mask)}/{len(z_flat)} 个点进行平面拟合")
        
        # 使用正常点拟合平面 z = ax + by + c
        x_normal = x_flat[normal_mask]
        y_normal = y_flat[normal_mask]
        z_normal = z_flat[normal_mask]
        
        # 构建设计矩阵
        A = np.column_stack([x_normal, y_normal, np.ones(len(x_normal))])
        
        # 最小二乘拟合
        coeffs, residuals, rank, s = np.linalg.lstsq(A, z_normal, rcond=None)
        self.plane_coeffs = coeffs  # [a, b, c]
        
        # 计算拟合平面
        self.fitted_plane = coeffs[0] * self.X + coeffs[1] * self.Y + coeffs[2]
        
        # 计算校正后的高度（相对于拟合平面）
        self.corrected_height = self.height_matrix - self.fitted_plane
        
        print(f"平面方程: z = {coeffs[0]:.6f}*x + {coeffs[1]:.6f}*y + {coeffs[2]:.6f}")
        print(f"拟合残差RMS: {np.sqrt(np.mean(residuals)) if len(residuals) > 0 else 'N/A':.6f}μm")
        
    def detect_pillars(self, min_height_ratio=0.6):
        """检测柱子中心"""
        print("正在检测柱子...")
        
        # 使用校正后的高度
        height_data = self.corrected_height
        
        # 自适应阈值：基于高度分布
        height_flat = height_data.flatten()
        height_sorted = np.sort(height_flat)
        
        # 找到高度的显著跳跃点作为柱子阈值
        n_points = len(height_sorted)
        high_percentile = height_sorted[int(n_points * (1 - min_height_ratio))]
        
        # 进一步优化阈值：寻找梯度最大的点
        hist, bins = np.histogram(height_flat, bins=100)
        bin_centers = (bins[:-1] + bins[1:]) / 2
        
        # 找到直方图中的显著峰值
        peaks = []
        for i in range(1, len(hist) - 1):
            if hist[i] > hist[i-1] and hist[i] > hist[i+1] and hist[i] > np.max(hist) * 0.1:
                peaks.append(bin_centers[i])
        
        if len(peaks) >= 2:
            # 如果有多个峰值，选择较高的作为柱子阈值
            pillar_threshold = max(peaks)
        else:
            pillar_threshold = high_percentile
        
        print(f"柱子检测阈值: {pillar_threshold:.6f}μm")
        
        # 创建二值图像
        binary_mask = height_data > pillar_threshold
        
        # 形态学操作去除噪声
        kernel = np.ones((3, 3), np.uint8)
        binary_mask = ndimage.binary_opening(binary_mask, kernel)
        binary_mask = ndimage.binary_closing(binary_mask, kernel)
        
        # 连通域分析
        labeled_array, num_features = ndimage.label(binary_mask)
        
        self.pillar_centers = []
        self.pillar_regions = []
        
        for i in range(1, num_features + 1):
            region_mask = labeled_array == i
            region_size = np.sum(region_mask)
            
            # 过滤太小的区域（可能是噪声）
            if region_size < 10:
                continue
                
            # 计算区域的质心
            y_indices, x_indices = np.where(region_mask)
            center_x = np.mean(x_indices)
            center_y = np.mean(y_indices)
            
            # 转换为实际坐标
            real_x = center_x * self.x_step
            real_y = center_y * self.y_step
            
            self.pillar_centers.append((center_x, center_y, real_x, real_y))
            self.pillar_regions.append(region_mask)
        
        print(f"检测到 {len(self.pillar_centers)} 个柱子")
        
    def measure_pillar_height(self, pillar_idx, radius_um=1.5):
        """测量指定柱子的高度（3微米直径圆形区域的平均值）"""
        if pillar_idx >= len(self.pillar_centers):
            return None
            
        center_x, center_y, real_x, real_y = self.pillar_centers[pillar_idx]
        
        # 计算3微米直径圆形区域
        radius_pixels_x = radius_um / self.x_step
        radius_pixels_y = radius_um / self.y_step
        
        # 创建圆形掩码
        x_indices, y_indices = np.ogrid[:self.x_len, :self.y_len]
        distance_sq = ((x_indices - center_x) / radius_pixels_x) ** 2 + \
                     ((y_indices - center_y) / radius_pixels_y) ** 2
        circle_mask = distance_sq <= 1
        
        # 计算圆形区域内的平均高度
        if np.sum(circle_mask) > 0:
            avg_height = np.mean(self.corrected_height[circle_mask])
            return avg_height, circle_mask
        else:
            return None, None
    
    def measure_pillar_dimensions(self, pillar_idx):
        """测量柱子的X/Y方向尺寸"""
        if pillar_idx >= len(self.pillar_centers):
            return None

        center_x, center_y, real_x, real_y = self.pillar_centers[pillar_idx]
        center_x = max(0, min(int(center_x), self.x_len - 1))
        center_y = max(0, min(int(center_y), self.y_len - 1))
        
        # 获取柱子高度
        height_result = self.measure_pillar_height(pillar_idx)
        if height_result[0] is None:
            return None
            
        max_height = height_result[0]
        
        # 计算95%和10%高度位置
        height_95 = max_height * 0.95
        height_10 = max_height * 0.10
        
        results = {}
        
        # X方向切面
        if 0 <= center_y < self.y_len:
            x_profile = self.corrected_height[:, center_y]
            x_coords_profile = self.x_coords
            
            # 找到95%和10%高度位置的宽度
            x_width_95 = self._find_width_at_height(x_coords_profile, x_profile, height_95, real_x)
            x_width_10 = self._find_width_at_height(x_coords_profile, x_profile, height_10, real_x)
            
            results['x_profile'] = (x_coords_profile, x_profile)
            results['x_width_95'] = x_width_95
            results['x_width_10'] = x_width_10
        
        # Y方向切面
        if 0 <= center_x < self.x_len:
            y_profile = self.corrected_height[center_x, :]
            y_coords_profile = self.y_coords
            
            # 找到95%和10%高度位置的宽度
            y_width_95 = self._find_width_at_height(y_coords_profile, y_profile, height_95, real_y)
            y_width_10 = self._find_width_at_height(y_coords_profile, y_profile, height_10, real_y)
            
            results['y_profile'] = (y_coords_profile, y_profile)
            results['y_width_95'] = y_width_95
            results['y_width_10'] = y_width_10
        
        results['height_95'] = height_95
        results['height_10'] = height_10
        results['max_height'] = max_height
        
        return results
    
    def _find_width_at_height(self, coords, profile, target_height, center_coord):
        """在指定高度找到轮廓宽度"""
        # 找到高于目标高度的点
        above_height = profile >= target_height
        
        if not np.any(above_height):
            return 0
        
        # 找到连续区域
        diff = np.diff(np.concatenate(([False], above_height, [False])).astype(int))
        starts = np.where(diff == 1)[0]
        ends = np.where(diff == -1)[0]
        
        if len(starts) == 0 or len(ends) == 0:
            return 0
        
        # 找到包含中心点的区域
        center_idx = np.argmin(np.abs(coords - center_coord))
        
        for start, end in zip(starts, ends):
            if start <= center_idx < end:
                return coords[end-1] - coords[start]
        
        # 如果没找到包含中心的区域，返回最大区域
        max_width = 0
        for start, end in zip(starts, ends):
            width = coords[end-1] - coords[start]
            max_width = max(max_width, width)
        
        return max_width

    def visualize_plane_fitting(self):
        """可视化平面拟合前后的对比"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 原始高度图
        im1 = axes[0, 0].imshow(self.height_matrix.T, origin='lower',
                               extent=[0, self.x_coords[-1], 0, self.y_coords[-1]],
                               cmap='viridis', aspect='equal')
        axes[0, 0].set_title('原始高度图')
        axes[0, 0].set_xlabel('X (μm)')
        axes[0, 0].set_ylabel('Y (μm)')
        plt.colorbar(im1, ax=axes[0, 0], label='高度 (μm)')

        # 拟合的平面
        im2 = axes[0, 1].imshow(self.fitted_plane.T, origin='lower',
                               extent=[0, self.x_coords[-1], 0, self.y_coords[-1]],
                               cmap='viridis', aspect='equal')
        axes[0, 1].set_title('拟合的基准平面')
        axes[0, 1].set_xlabel('X (μm)')
        axes[0, 1].set_ylabel('Y (μm)')
        plt.colorbar(im2, ax=axes[0, 1], label='高度 (μm)')

        # 校正后的高度图
        im3 = axes[1, 0].imshow(self.corrected_height.T, origin='lower',
                               extent=[0, self.x_coords[-1], 0, self.y_coords[-1]],
                               cmap='viridis', aspect='equal')
        axes[1, 0].set_title('校正后高度图（相对基准面）')
        axes[1, 0].set_xlabel('X (μm)')
        axes[1, 0].set_ylabel('Y (μm)')
        plt.colorbar(im3, ax=axes[1, 0], label='相对高度 (μm)')

        # 高度分布直方图
        axes[1, 1].hist(self.height_matrix.flatten(), bins=50, alpha=0.7, label='原始高度', density=True)
        axes[1, 1].hist(self.corrected_height.flatten(), bins=50, alpha=0.7, label='校正后高度', density=True)
        axes[1, 1].set_xlabel('高度 (μm)')
        axes[1, 1].set_ylabel('概率密度')
        axes[1, 1].set_title('高度分布对比')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def visualize_pillar_detection(self):
        """可视化柱子检测结果"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))

        # 校正后的高度图 + 检测到的柱子中心
        im1 = axes[0].imshow(self.corrected_height.T, origin='lower',
                            extent=[0, self.x_coords[-1], 0, self.y_coords[-1]],
                            cmap='viridis', aspect='equal')
        axes[0].set_title(f'柱子检测结果 (共{len(self.pillar_centers)}个)')
        axes[0].set_xlabel('X (μm)')
        axes[0].set_ylabel('Y (μm)')

        # 标记柱子中心
        for i, (cx, cy, real_x, real_y) in enumerate(self.pillar_centers):
            axes[0].plot(real_x, real_y, 'r+', markersize=10, markeredgewidth=2)
            axes[0].text(real_x, real_y + 5, f'P{i+1}', color='red',
                        ha='center', va='bottom', fontweight='bold')

        plt.colorbar(im1, ax=axes[0], label='相对高度 (μm)')

        # 3D视图
        ax2 = fig.add_subplot(122, projection='3d')

        # 降采样以提高性能
        step = max(1, min(self.x_len, self.y_len) // 100)
        X_sub = self.X[::step, ::step]
        Y_sub = self.Y[::step, ::step]
        Z_sub = self.corrected_height[::step, ::step]

        ax2.plot_surface(X_sub, Y_sub, Z_sub, cmap='viridis', alpha=0.8)

        # 标记柱子位置
        for i, (cx, cy, real_x, real_y) in enumerate(self.pillar_centers):
            # 确保索引在有效范围内
            cx_safe = max(0, min(int(cx), self.x_len - 1))
            cy_safe = max(0, min(int(cy), self.y_len - 1))
            height_at_center = self.corrected_height[cx_safe, cy_safe]
            ax2.scatter([real_x], [real_y], [height_at_center],
                       color='red', s=100, marker='^')

        ax2.set_xlabel('X (μm)')
        ax2.set_ylabel('Y (μm)')
        ax2.set_zlabel('相对高度 (μm)')
        ax2.set_title('3D表面视图')

        plt.tight_layout()
        plt.show()

    def visualize_pillar_measurement(self, pillar_idx):
        """可视化单个柱子的测量结果"""
        if pillar_idx >= len(self.pillar_centers):
            print(f"柱子索引 {pillar_idx} 超出范围")
            return

        # 获取测量结果
        height_result = self.measure_pillar_height(pillar_idx)
        dimension_result = self.measure_pillar_dimensions(pillar_idx)

        if height_result[0] is None or dimension_result is None:
            print(f"无法测量柱子 {pillar_idx}")
            return

        avg_height, circle_mask = height_result
        center_x, center_y, real_x, real_y = self.pillar_centers[pillar_idx]

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 柱子区域高度图 + 测量圆形
        im1 = axes[0, 0].imshow(self.corrected_height.T, origin='lower',
                               extent=[0, self.x_coords[-1], 0, self.y_coords[-1]],
                               cmap='viridis', aspect='equal')
        axes[0, 0].set_title(f'柱子 P{pillar_idx+1} - 高度测量区域')
        axes[0, 0].set_xlabel('X (μm)')
        axes[0, 0].set_ylabel('Y (μm)')

        # 绘制测量圆形
        circle = plt.Circle((real_x, real_y), 1.5, fill=False, color='red', linewidth=2)
        axes[0, 0].add_patch(circle)
        axes[0, 0].plot(real_x, real_y, 'r+', markersize=10, markeredgewidth=2)

        # 设置合适的显示范围
        margin = 20  # μm
        axes[0, 0].set_xlim(real_x - margin, real_x + margin)
        axes[0, 0].set_ylim(real_y - margin, real_y + margin)
        plt.colorbar(im1, ax=axes[0, 0], label='相对高度 (μm)')

        # X方向切面
        if 'x_profile' in dimension_result:
            x_coords_profile, x_profile = dimension_result['x_profile']
            axes[0, 1].plot(x_coords_profile, x_profile, 'b-', linewidth=2, label='高度轮廓')
            axes[0, 1].axhline(y=dimension_result['height_95'], color='r', linestyle='--',
                              label=f"95%高度 ({dimension_result['height_95']:.3f}μm)")
            axes[0, 1].axhline(y=dimension_result['height_10'], color='g', linestyle='--',
                              label=f"10%高度 ({dimension_result['height_10']:.3f}μm)")
            axes[0, 1].axvline(x=real_x, color='k', linestyle=':', alpha=0.5, label='中心线')

            axes[0, 1].set_title(f'X方向切面 (Y={real_y:.1f}μm)')
            axes[0, 1].set_xlabel('X (μm)')
            axes[0, 1].set_ylabel('相对高度 (μm)')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

        # Y方向切面
        if 'y_profile' in dimension_result:
            y_coords_profile, y_profile = dimension_result['y_profile']
            axes[1, 0].plot(y_coords_profile, y_profile, 'b-', linewidth=2, label='高度轮廓')
            axes[1, 0].axhline(y=dimension_result['height_95'], color='r', linestyle='--',
                              label=f"95%高度 ({dimension_result['height_95']:.3f}μm)")
            axes[1, 0].axhline(y=dimension_result['height_10'], color='g', linestyle='--',
                              label=f"10%高度 ({dimension_result['height_10']:.3f}μm)")
            axes[1, 0].axvline(x=real_y, color='k', linestyle=':', alpha=0.5, label='中心线')

            axes[1, 0].set_title(f'Y方向切面 (X={real_x:.1f}μm)')
            axes[1, 0].set_xlabel('Y (μm)')
            axes[1, 0].set_ylabel('相对高度 (μm)')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # 测量结果汇总
        axes[1, 1].axis('off')
        result_text = f"""柱子 P{pillar_idx+1} 测量结果:

位置: ({real_x:.2f}, {real_y:.2f}) μm

高度测量:
• 平均高度: {avg_height:.3f} μm
• 测量区域: 直径3μm圆形

X方向尺寸:
• 95%高度位置宽度: {dimension_result.get('x_width_95', 0):.3f} μm
• 10%高度位置宽度: {dimension_result.get('x_width_10', 0):.3f} μm

Y方向尺寸:
• 95%高度位置宽度: {dimension_result.get('y_width_95', 0):.3f} μm
• 10%高度位置宽度: {dimension_result.get('y_width_10', 0):.3f} μm"""

        axes[1, 1].text(0.1, 0.9, result_text, transform=axes[1, 1].transAxes,
                        fontsize=12, verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()
        plt.show()

        return {
            'pillar_id': pillar_idx + 1,
            'position': (real_x, real_y),
            'height': avg_height,
            'x_width_95': dimension_result.get('x_width_95', 0),
            'x_width_10': dimension_result.get('x_width_10', 0),
            'y_width_95': dimension_result.get('y_width_95', 0),
            'y_width_10': dimension_result.get('y_width_10', 0)
        }

    def measure_all_pillars(self):
        """测量所有检测到的柱子"""
        results = []
        print(f"\n开始测量所有 {len(self.pillar_centers)} 个柱子...")

        for i in range(len(self.pillar_centers)):
            print(f"正在测量柱子 P{i+1}...")
            result = self.visualize_pillar_measurement(i)
            if result:
                results.append(result)

        return results

    def print_summary(self, results):
        """打印测量结果汇总"""
        print("\n" + "="*80)
        print("测量结果汇总")
        print("="*80)

        if not results:
            print("未检测到有效的柱子")
            return

        print(f"{'柱子ID':<8} {'位置(μm)':<20} {'高度(μm)':<12} {'X95%(μm)':<12} {'X10%(μm)':<12} {'Y95%(μm)':<12} {'Y10%(μm)':<12}")
        print("-" * 80)

        for result in results:
            pos_str = f"({result['position'][0]:.1f},{result['position'][1]:.1f})"
            print(f"P{result['pillar_id']:<7} {pos_str:<20} {result['height']:<12.3f} "
                  f"{result['x_width_95']:<12.3f} {result['x_width_10']:<12.3f} "
                  f"{result['y_width_95']:<12.3f} {result['y_width_10']:<12.3f}")

        print("-" * 80)
        print(f"总计检测到 {len(results)} 个柱子")


def main():
    """主函数 - 演示完整的测量流程"""
    print("DAZ 3D表面测量演示程序")
    print("="*50)

    # 读取数据
    file_path = "N001.TXT"  # 可以修改为其他文件路径

    try:
        # 初始化测量对象
        measurer = DAZMeasurement(file_path)

        # 步骤1: 平面拟合和校正
        print("\n步骤1: 平面拟合和校正")
        measurer.fit_base_plane()
        measurer.visualize_plane_fitting()

        # 步骤2: 柱子检测
        print("\n步骤2: 柱子检测")
        measurer.detect_pillars()
        measurer.visualize_pillar_detection()

        # 步骤3: 测量所有柱子
        print("\n步骤3: 柱子尺寸测量")
        results = measurer.measure_all_pillars()

        # 步骤4: 结果汇总
        print("\n步骤4: 结果汇总")
        measurer.print_summary(results)

        print("\n测量完成！")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        print("请确保文件存在，或修改 file_path 变量指向正确的文件")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
