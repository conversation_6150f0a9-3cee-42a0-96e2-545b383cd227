阶段1：数据处理和基础设施
扩展现有的数据读取功能
支持多种DAZ文件格式
数据验证和错误处理
内存优化的大文件处理
建立坐标系统
统一的坐标系定义
单位转换（确保微米精度）
数据结构优化
阶段2：平面拟合和校正
噪声识别和过滤
统计方法识别异常值
形态学操作去除小噪声
保留柱子特征的同时去除干扰
基准平面拟合
使用RANSAC算法进行鲁棒平面拟合
排除柱子区域的影响
处理轻微倾斜和翘曲
表面校正
基于拟合平面进行倾角校正
生成校正后的高度图
阶段3：柱子检测和分割
候选区域检测
基于高度阈值的初步筛选
连通域分析
形状特征过滤（近似圆形）
精确分割
分水岭算法分离相邻柱子
边界优化
中心点定位
阶段4：尺寸测量
高度测量
柱子中心3微米圆形区域定义
平均高度计算
相对基准平面的净高度
X/Y方向尺寸测量
过中心的X/Y方向截面提取
95%和10%高度位置确定
水平线与截面交点距离计算
阶段5：结果输出和验证
结果整合
多柱子测量结果汇总
数据质量评估
异常结果标记
可视化和报告
3D表面可视化
测量结果图表
详细测量报告

阶段1.1可先搁置，我们现在依靠私有软件将daz转换为txt格式，后期需要逆向这一过程，让我们的软件可以直接处理daz文件。
阶段1.2：当前脚本的daz_matrix得到的就是笛卡尔系下的数据结构。daz_matrix[x1][y1]的值对应了x1*x_step、y1*y_step位置的高度
阶段2看起来没有什么问题。我们需要明确的一点是我不希望引入过多硬编码来实现这步识别，例如不假定柱子高度、不假定噪声图案面积。有一点先验知识是：柱子高度显著高于平面及噪声；平面相对平整，噪声图案会比较明显
输出的格式：每个txt文件输出一张概览图，标识识别到的柱子中心、编号和量测数值。每个柱子输出两张截面图，