#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DAZ 3D表面测量演示程序 - 单柱子详细分析
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
from daz_measurement_demo import DAZMeasurement

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

def demo_single_pillar_analysis():
    """演示单个柱子的详细分析过程"""
    print("DAZ 3D表面测量 - 单柱子详细分析演示")
    print("="*60)
    
    # 读取数据
    file_path = "N001.TXT"
    measurer = DAZMeasurement(file_path)
    
    # 平面拟合
    print("\n1. 执行平面拟合和校正...")
    measurer.fit_base_plane()
    
    # 柱子检测
    print("\n2. 检测柱子...")
    measurer.detect_pillars()
    
    if len(measurer.pillar_centers) == 0:
        print("未检测到柱子！")
        return
    
    # 显示平面拟合结果
    print("\n3. 显示平面拟合结果...")
    measurer.visualize_plane_fitting()
    
    # 显示柱子检测结果
    print("\n4. 显示柱子检测结果...")
    measurer.visualize_pillar_detection()
    
    # 选择一个高度较高的柱子进行详细分析
    print("\n5. 选择柱子进行详细分析...")
    
    # 计算所有柱子的高度，选择最高的几个
    pillar_heights = []
    for i in range(len(measurer.pillar_centers)):
        height_result = measurer.measure_pillar_height(i)
        if height_result[0] is not None:
            pillar_heights.append((i, height_result[0]))
    
    # 按高度排序，选择最高的柱子
    pillar_heights.sort(key=lambda x: x[1], reverse=True)
    
    print(f"检测到 {len(pillar_heights)} 个有效柱子")
    print("柱子高度排序（前5个）：")
    for i, (pillar_idx, height) in enumerate(pillar_heights[:5]):
        center_x, center_y, real_x, real_y = measurer.pillar_centers[pillar_idx]
        print(f"  P{pillar_idx+1}: 高度={height:.3f}μm, 位置=({real_x:.1f}, {real_y:.1f})μm")
    
    # 分析前3个最高的柱子
    print(f"\n6. 详细分析前3个最高的柱子...")
    
    for i, (pillar_idx, height) in enumerate(pillar_heights[:3]):
        print(f"\n--- 分析柱子 P{pillar_idx+1} (高度: {height:.3f}μm) ---")
        result = measurer.visualize_pillar_measurement(pillar_idx)
        
        if result:
            print(f"测量结果:")
            print(f"  位置: ({result['position'][0]:.2f}, {result['position'][1]:.2f}) μm")
            print(f"  高度: {result['height']:.3f} μm")
            print(f"  X方向 - 95%位置宽度: {result['x_width_95']:.3f} μm")
            print(f"  X方向 - 10%位置宽度: {result['x_width_10']:.3f} μm")
            print(f"  Y方向 - 95%位置宽度: {result['y_width_95']:.3f} μm")
            print(f"  Y方向 - 10%位置宽度: {result['y_width_10']:.3f} μm")
        
        # 暂停，让用户查看图表
        input("按回车键继续分析下一个柱子...")
    
    print("\n演示完成！")

def demo_measurement_process():
    """演示完整的测量流程"""
    print("DAZ 3D表面测量 - 完整流程演示")
    print("="*50)
    
    # 读取数据
    file_path = "N001.TXT"
    measurer = DAZMeasurement(file_path)
    
    # 步骤1: 平面拟合
    print("\n步骤1: 平面拟合和校正")
    measurer.fit_base_plane()
    
    # 步骤2: 柱子检测
    print("\n步骤2: 柱子检测")
    measurer.detect_pillars()
    
    # 步骤3: 显示检测结果
    print("\n步骤3: 显示检测结果")
    measurer.visualize_pillar_detection()
    
    # 步骤4: 测量所有柱子
    print("\n步骤4: 测量所有柱子")
    results = []
    
    for i in range(len(measurer.pillar_centers)):
        print(f"正在测量柱子 P{i+1}...")
        
        # 获取高度
        height_result = measurer.measure_pillar_height(i)
        if height_result[0] is None:
            continue
            
        # 获取尺寸
        dimension_result = measurer.measure_pillar_dimensions(i)
        if dimension_result is None:
            continue
            
        center_x, center_y, real_x, real_y = measurer.pillar_centers[i]
        
        result = {
            'pillar_id': i + 1,
            'position': (real_x, real_y),
            'height': height_result[0],
            'x_width_95': dimension_result.get('x_width_95', 0),
            'x_width_10': dimension_result.get('x_width_10', 0),
            'y_width_95': dimension_result.get('y_width_95', 0),
            'y_width_10': dimension_result.get('y_width_10', 0)
        }
        results.append(result)
    
    # 步骤5: 结果汇总
    print("\n步骤5: 结果汇总")
    measurer.print_summary(results)
    
    return results

if __name__ == "__main__":
    print("请选择演示模式:")
    print("1. 单柱子详细分析")
    print("2. 完整测量流程")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        demo_single_pillar_analysis()
    elif choice == "2":
        demo_measurement_process()
    else:
        print("无效选择，运行完整测量流程...")
        demo_measurement_process()
