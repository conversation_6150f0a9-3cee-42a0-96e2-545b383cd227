# DAZ 3D表面测量演示程序

这是一个用于处理DAZ格式3D表面数据的Python程序，能够自动检测和测量圆台形柱子的尺寸。

## 功能特点

1. **数据读取和解析**: 读取DAZ格式的3D表面数据
2. **自适应平面拟合**: 自动识别和排除异常值，拟合基准平面
3. **表面校正**: 基于拟合平面校正表面倾角
4. **柱子检测**: 自动检测圆台形柱子的位置
5. **尺寸测量**: 
   - 柱子高度（3μm直径圆形区域平均值）
   - X/Y方向95%和10%高度位置的尺寸
6. **可视化**: 完整的测量过程和结果可视化

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 将DAZ数据文件放在程序目录下
2. 修改 `daz_measurement_demo.py` 中的 `file_path` 变量指向您的数据文件
3. 运行程序：

```bash
python daz_measurement_demo.py
```

## 程序流程

1. **数据加载**: 读取DAZ文件，解析头部信息和高度数据
2. **平面拟合**: 使用统计方法自动排除异常值，拟合基准平面
3. **表面校正**: 计算相对于基准平面的校正高度
4. **柱子检测**: 基于高度分布自适应确定阈值，检测柱子区域
5. **尺寸测量**: 对每个检测到的柱子进行精确测量
6. **结果输出**: 生成详细的测量报告和可视化图表

## 技术特点

- **自适应算法**: 不依赖硬编码阈值，根据数据分布自动调整参数
- **鲁棒性**: 能够处理噪声和表面不平整
- **高精度**: 支持微米级精度的测量
- **可视化**: 提供完整的处理过程可视化

## 输出结果

程序会生成以下可视化图表：
1. 平面拟合前后对比图
2. 柱子检测结果图（2D和3D视图）
3. 每个柱子的详细测量图（包含X/Y方向切面）
4. 测量结果汇总表

## 注意事项

- 确保matplotlib能正确显示中文字体
- 数据文件格式需符合DAZ标准
- 程序会自动处理大部分参数，但可根据需要调整算法参数
